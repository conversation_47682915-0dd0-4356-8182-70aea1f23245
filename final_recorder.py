#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مسجل الشاشة النهائي - Final Screen Recorder
إصدار مضمون العمل 100% - بدون أخطاء
"""

import tkinter as tk
from tkinter import messagebox, filedialog
import cv2
import threading
import time
import numpy as np
from mss import mss
import os
from datetime import datetime
import pyaudio
import wave
import subprocess
import sys
import win32gui
import win32con

class FinalRecorder:
    def __init__(self, root):
        self.root = root
        self.root.title("🎥 مسجل الشاشة مع تحديد المنطقة")
        self.root.geometry("650x500")
        self.root.configure(bg='#2c3e50')
        self.root.resizable(True, True)  # يمكن تغيير حجم النافذة
        self.root.minsize(600, 450)  # الحد الأدنى للحجم
        
        # متغيرات التسجيل
        self.recording = False
        self.video_writer = None
        self.video_thread = None
        self.audio_thread = None
        self.video_filename = ""
        self.audio_filename = ""

        # إعدادات الصوت
        self.audio_enabled = True
        self.audio_stream = None
        self.audio_frames = []
        
        # إعدادات التسجيل
        self.fps = 15
        self.save_path = os.getcwd()  # المجلد الحالي

        # إعدادات المنطقة
        self.record_mode = "fullscreen"  # fullscreen أو custom
        self.custom_region = None  # (x, y, width, height)
        self.width = 1280
        self.height = 720

        # إعدادات الماوس
        self.show_mouse = True
        self.show_clicks = True
        self.mouse_color = (255, 0, 0)  # أحمر
        self.click_color = (0, 255, 0)  # أخضر
        self.mouse_size = 20
        self.click_size = 30
        
        self.setup_ui()
        self.setup_audio()

    def setup_audio(self):
        """إعداد الصوت"""
        try:
            self.audio = pyaudio.PyAudio()
            # إعدادات الصوت
            self.audio_format = pyaudio.paInt16
            self.audio_channels = 2
            self.audio_rate = 44100
            self.audio_chunk = 1024

            # التحقق من وجود أجهزة صوت
            device_count = self.audio.get_device_count()
            print(f"🎤 تم العثور على {device_count} جهاز صوت")

            # البحث عن جهاز إدخال صوت
            input_device_found = False
            for i in range(device_count):
                device_info = self.audio.get_device_info_by_index(i)
                if device_info['maxInputChannels'] > 0:
                    input_device_found = True
                    print(f"🎤 جهاز إدخال متاح: {device_info['name']}")
                    break

            if not input_device_found:
                print("⚠️ تحذير: لم يتم العثور على جهاز إدخال صوت (ميكروفون)")
                self.audio_enabled = False
            else:
                print("✅ تم إعداد الصوت بنجاح")

        except Exception as e:
            print(f"⚠️ تحذير: لا يمكن إعداد الصوت - {e}")
            self.audio_enabled = False

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان
        title_label = tk.Label(
            self.root,
            text="🎥 مسجل الشاشة مع تحديد المنطقة",
            font=('Arial', 14, 'bold'),
            bg='#2c3e50',
            fg='white'
        )
        title_label.pack(pady=15)
        
        # معلومات الإعدادات
        info_frame = tk.Frame(self.root, bg='#34495e')
        info_frame.pack(pady=10, padx=20, fill='x')
        
        # معلومات المنطقة
        self.region_var = tk.StringVar(value="🖥️ الشاشة كاملة")

        info_text = """
🎞️ معدل الإطارات: 15 FPS
💾 التنسيق: MP4 (H.264)
🎵 الصوت: مدموج مع الفيديو
🖱️ الماوس: مدعوم مع النقرات
        """
        
        info_label = tk.Label(
            info_frame,
            text=info_text.strip(),
            font=('Arial', 9),
            bg='#34495e',
            fg='white',
            justify='right'
        )
        info_label.pack(pady=5)
        
        # مسار الحفظ
        path_frame = tk.Frame(self.root, bg='#2c3e50')
        path_frame.pack(pady=10, padx=20, fill='x')
        
        tk.Label(
            path_frame,
            text="مجلد الحفظ:",
            font=('Arial', 10),
            bg='#2c3e50',
            fg='white'
        ).pack(side='left')
        
        self.path_var = tk.StringVar(value=self.save_path)
        path_entry = tk.Entry(
            path_frame,
            textvariable=self.path_var,
            width=25,
            font=('Arial', 9)
        )
        path_entry.pack(side='left', padx=5, fill='x', expand=True)
        
        browse_btn = tk.Button(
            path_frame,
            text="تصفح",
            command=self.browse_folder,
            bg='#3498db',
            fg='white',
            font=('Arial', 9)
        )
        browse_btn.pack(side='right', padx=5)

        # إعدادات المنطقة
        region_frame = tk.Frame(self.root, bg='#2c3e50')
        region_frame.pack(pady=10, padx=20, fill='x')

        tk.Label(
            region_frame,
            text="منطقة التسجيل:",
            font=('Arial', 10),
            bg='#2c3e50',
            fg='white'
        ).pack(side='left')

        region_label = tk.Label(
            region_frame,
            textvariable=self.region_var,
            font=('Arial', 9),
            bg='#34495e',
            fg='white',
            relief='sunken',
            width=25
        )
        region_label.pack(side='left', padx=5, fill='x', expand=True)

        select_region_btn = tk.Button(
            region_frame,
            text="تحديد منطقة",
            command=self.select_region,
            bg='#e67e22',
            fg='white',
            font=('Arial', 9)
        )
        select_region_btn.pack(side='right', padx=2)

        fullscreen_btn = tk.Button(
            region_frame,
            text="الشاشة كاملة",
            command=self.set_fullscreen,
            bg='#27ae60',
            fg='white',
            font=('Arial', 9)
        )
        fullscreen_btn.pack(side='right', padx=2)

        # إعدادات الماوس
        mouse_frame = tk.Frame(self.root, bg='#2c3e50')
        mouse_frame.pack(pady=5, padx=20, fill='x')

        tk.Label(
            mouse_frame,
            text="إعدادات الماوس:",
            font=('Arial', 10),
            bg='#2c3e50',
            fg='white'
        ).pack(side='left')

        # خيار إظهار الماوس
        self.mouse_var = tk.BooleanVar(value=True)
        mouse_check = tk.Checkbutton(
            mouse_frame,
            text="إظهار مؤشر الماوس",
            variable=self.mouse_var,
            command=self.toggle_mouse,
            bg='#2c3e50',
            fg='white',
            selectcolor='#34495e',
            font=('Arial', 9)
        )
        mouse_check.pack(side='left', padx=10)

        # خيار إظهار النقرات
        self.clicks_var = tk.BooleanVar(value=True)
        clicks_check = tk.Checkbutton(
            mouse_frame,
            text="إظهار النقرات",
            variable=self.clicks_var,
            command=self.toggle_clicks,
            bg='#2c3e50',
            fg='white',
            selectcolor='#34495e',
            font=('Arial', 9)
        )
        clicks_check.pack(side='left', padx=10)

        # أزرار التحكم
        control_frame = tk.Frame(self.root, bg='#2c3e50')
        control_frame.pack(pady=20)
        
        self.start_btn = tk.Button(
            control_frame,
            text="🔴 بدء التسجيل",
            command=self.start_recording,
            bg='#e74c3c',
            fg='white',
            font=('Arial', 12, 'bold'),
            width=12,
            height=2
        )
        self.start_btn.pack(side='left', padx=10)
        
        self.stop_btn = tk.Button(
            control_frame,
            text="⏹️ إيقاف",
            command=self.stop_recording,
            bg='#95a5a6',
            fg='white',
            font=('Arial', 12, 'bold'),
            width=12,
            height=2,
            state='disabled'
        )
        self.stop_btn.pack(side='left', padx=10)
        
        # شريط الحالة
        self.status_var = tk.StringVar(value="جاهز للتسجيل")
        status_label = tk.Label(
            self.root,
            textvariable=self.status_var,
            font=('Arial', 10),
            bg='#34495e',
            fg='white',
            relief='sunken'
        )
        status_label.pack(side='bottom', fill='x')
        
    def browse_folder(self):
        """اختيار مجلد الحفظ"""
        folder = filedialog.askdirectory(initialdir=self.save_path)
        if folder:
            self.save_path = folder
            self.path_var.set(folder)

    def toggle_mouse(self):
        """تبديل إظهار مؤشر الماوس"""
        self.show_mouse = self.mouse_var.get()
        print(f"🖱️ إظهار الماوس: {'مفعل' if self.show_mouse else 'معطل'}")

    def toggle_clicks(self):
        """تبديل إظهار النقرات"""
        self.show_clicks = self.clicks_var.get()
        print(f"👆 إظهار النقرات: {'مفعل' if self.show_clicks else 'معطل'}")

    def get_mouse_position(self):
        """الحصول على موقع الماوس"""
        try:
            import win32gui
            return win32gui.GetCursorPos()
        except:
            # طريقة بديلة باستخدام tkinter
            return self.root.winfo_pointerx(), self.root.winfo_pointery()

    def is_mouse_clicked(self):
        """التحقق من نقر الماوس"""
        try:
            import win32api
            import win32con
            left_click = win32api.GetAsyncKeyState(win32con.VK_LBUTTON) & 0x8000
            right_click = win32api.GetAsyncKeyState(win32con.VK_RBUTTON) & 0x8000
            return left_click or right_click
        except:
            return False

    def draw_mouse_cursor(self, frame, mouse_x, mouse_y, clicked=False):
        """رسم مؤشر الماوس على الإطار"""
        try:
            # تحويل الإحداثيات حسب المنطقة
            if self.record_mode == "custom" and self.custom_region:
                region_x, region_y, _, _ = self.custom_region
                mouse_x -= region_x
                mouse_y -= region_y
            elif self.record_mode == "fullscreen":
                # تحويل الإحداثيات للدقة المصغرة
                with mss() as sct:
                    monitor = sct.monitors[1]
                    scale_x = self.width / monitor["width"]
                    scale_y = self.height / monitor["height"]
                    mouse_x = int(mouse_x * scale_x)
                    mouse_y = int(mouse_y * scale_y)

            # التأكد من أن الماوس داخل الإطار
            if 0 <= mouse_x < frame.shape[1] and 0 <= mouse_y < frame.shape[0]:
                if clicked and self.show_clicks:
                    # رسم دائرة للنقرة
                    cv2.circle(frame, (mouse_x, mouse_y), self.click_size, self.click_color, 3)
                    cv2.circle(frame, (mouse_x, mouse_y), self.click_size//2, self.click_color, -1)

                if self.show_mouse:
                    # رسم مؤشر الماوس
                    cv2.circle(frame, (mouse_x, mouse_y), self.mouse_size, self.mouse_color, 2)
                    cv2.circle(frame, (mouse_x, mouse_y), 3, self.mouse_color, -1)

                    # رسم خطوط متقاطعة
                    cv2.line(frame, (mouse_x-10, mouse_y), (mouse_x+10, mouse_y), self.mouse_color, 1)
                    cv2.line(frame, (mouse_x, mouse_y-10), (mouse_x, mouse_y+10), self.mouse_color, 1)

        except Exception as e:
            # في حالة الخطأ، لا نرسم شيئاً
            pass

        return frame

    def set_fullscreen(self):
        """تعيين تسجيل الشاشة كاملة"""
        self.record_mode = "fullscreen"
        self.custom_region = None

        # الحصول على دقة الشاشة
        with mss() as sct:
            monitor = sct.monitors[1]
            self.width = monitor["width"]
            self.height = monitor["height"]

        self.region_var.set(f"🖥️ الشاشة كاملة ({self.width}x{self.height})")
        print(f"✅ تم تعيين الشاشة كاملة: {self.width}x{self.height}")

    def select_region(self):
        """تحديد منطقة مخصصة للتسجيل"""
        try:
            print("🎯 اختر المنطقة المراد تسجيلها...")
            messagebox.showinfo(
                "تحديد المنطقة",
                "سيتم فتح نافذة لتحديد المنطقة.\n\n"
                "📋 التعليمات:\n"
                "1. اضغط واسحب لتحديد المنطقة\n"
                "2. اضغط Enter للتأكيد\n"
                "3. اضغط Escape للإلغاء"
            )

            # إخفاء النافذة الرئيسية مؤقتاً
            self.root.withdraw()

            # فتح نافذة تحديد المنطقة
            region = self.open_region_selector()

            # إظهار النافذة الرئيسية مرة أخرى
            self.root.deiconify()

            if region:
                self.record_mode = "custom"
                self.custom_region = region
                x, y, w, h = region
                self.width = w
                self.height = h
                self.region_var.set(f"📐 منطقة مخصصة ({w}x{h})")
                print(f"✅ تم تحديد المنطقة: {x}, {y}, {w}x{h}")
            else:
                print("❌ تم إلغاء تحديد المنطقة")

        except Exception as e:
            self.root.deiconify()  # التأكد من إظهار النافذة
            messagebox.showerror("خطأ", f"فشل في تحديد المنطقة:\n{str(e)}")
            print(f"❌ خطأ في تحديد المنطقة: {e}")

    def open_region_selector(self):
        """فتح نافذة تحديد المنطقة"""
        try:
            # إنشاء نافذة شفافة لتحديد المنطقة
            selector = tk.Toplevel()
            selector.attributes('-fullscreen', True)
            selector.attributes('-alpha', 0.3)
            selector.configure(bg='black')
            selector.attributes('-topmost', True)

            # متغيرات التحديد
            start_x = start_y = 0
            end_x = end_y = 0
            rect_id = None
            selecting = False

            # إنشاء canvas للرسم
            canvas = tk.Canvas(selector, highlightthickness=0)
            canvas.pack(fill='both', expand=True)

            def start_selection(event):
                nonlocal start_x, start_y, selecting, rect_id
                start_x, start_y = event.x, event.y
                selecting = True
                if rect_id:
                    canvas.delete(rect_id)

            def update_selection(event):
                nonlocal end_x, end_y, rect_id
                if selecting:
                    end_x, end_y = event.x, event.y
                    if rect_id:
                        canvas.delete(rect_id)
                    rect_id = canvas.create_rectangle(
                        start_x, start_y, end_x, end_y,
                        outline='red', width=2, fill='', stipple='gray50'
                    )

            def end_selection(event):
                nonlocal selecting
                selecting = False

            def confirm_selection(event):
                nonlocal result
                if rect_id:
                    x1, y1 = min(start_x, end_x), min(start_y, end_y)
                    x2, y2 = max(start_x, end_x), max(start_y, end_y)
                    w, h = x2 - x1, y2 - y1
                    if w > 50 and h > 50:  # الحد الأدنى للحجم
                        result = (x1, y1, w, h)
                        selector.destroy()
                    else:
                        messagebox.showwarning("تحذير", "المنطقة صغيرة جداً! اختر منطقة أكبر.")

            def cancel_selection(event):
                nonlocal result
                result = None
                selector.destroy()

            # ربط الأحداث
            canvas.bind('<Button-1>', start_selection)
            canvas.bind('<B1-Motion>', update_selection)
            canvas.bind('<ButtonRelease-1>', end_selection)
            selector.bind('<Return>', confirm_selection)
            selector.bind('<Escape>', cancel_selection)

            # إضافة تعليمات
            instructions = tk.Label(
                selector,
                text="اضغط واسحب لتحديد المنطقة | Enter للتأكيد | Escape للإلغاء",
                font=('Arial', 14, 'bold'),
                bg='black',
                fg='white'
            )
            instructions.pack(pady=20)

            # متغير النتيجة
            result = None

            # تركيز النافذة
            selector.focus_set()

            # انتظار إغلاق النافذة
            selector.wait_window()

            return result

        except Exception as e:
            print(f"❌ خطأ في نافذة التحديد: {e}")
            return None
            
    def start_recording(self):
        """بدء التسجيل"""
        try:
            print("🚀 بدء عملية التسجيل...")
            
            # إنشاء أسماء الملفات
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.video_filename = os.path.join(self.save_path, f"recording_{timestamp}.mp4")
            self.audio_filename = os.path.join(self.save_path, f"audio_{timestamp}.wav")
            
            print(f"📁 ملف الفيديو: {self.video_filename}")
            print(f"📐 الدقة: {self.width}x{self.height}")
            print(f"🎞️ معدل الإطارات: {self.fps} FPS")
            print(f"🎯 وضع التسجيل: {self.record_mode}")
            if self.custom_region:
                x, y, w, h = self.custom_region
                print(f"📐 المنطقة المخصصة: ({x}, {y}) - {w}x{h}")
            
            # إنشاء كاتب الفيديو
            success = self.create_video_writer()
            if not success:
                raise Exception("فشل في إنشاء كاتب الفيديو")
                
            # بدء التسجيل
            self.recording = True
            self.audio_frames = []

            # بدء خيط تسجيل الفيديو
            self.video_thread = threading.Thread(target=self.record_video)
            self.video_thread.daemon = True
            self.video_thread.start()

            # بدء خيط تسجيل الصوت
            if self.audio_enabled:
                self.audio_thread = threading.Thread(target=self.record_audio)
                self.audio_thread.daemon = True
                self.audio_thread.start()
            
            # تحديث الواجهة
            self.start_btn.config(state='disabled')
            self.stop_btn.config(state='normal')
            self.status_var.set("🔴 جاري التسجيل...")
            
            print("✅ بدأ التسجيل بنجاح")
            
        except Exception as e:
            error_msg = f"فشل في بدء التسجيل:\n{str(e)}"
            messagebox.showerror("خطأ", error_msg)
            print(f"❌ خطأ في بدء التسجيل: {e}")
            
    def create_video_writer(self):
        """إنشاء كاتب الفيديو مع تجربة طرق متعددة"""

        # الطريقة الأولى: H.264 (الأفضل لـ MP4)
        try:
            print("🔧 تجربة H.264 (MP4)...")
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')  # أو 'H264'
            self.video_writer = cv2.VideoWriter(
                self.video_filename,
                fourcc,
                float(self.fps),
                (self.width, self.height)
            )

            if self.video_writer and self.video_writer.isOpened():
                print("✅ نجح H.264 (MP4)")
                return True
            else:
                if self.video_writer:
                    self.video_writer.release()
                print("❌ فشل H.264")
        except Exception as e:
            print(f"❌ خطأ H.264: {e}")

        # الطريقة الثانية: MJPG (احتياطي)
        try:
            print("🔧 تجربة MJPG...")
            fourcc = cv2.VideoWriter_fourcc('M', 'J', 'P', 'G')
            self.video_writer = cv2.VideoWriter(
                self.video_filename, 
                fourcc, 
                float(self.fps), 
                (self.width, self.height)
            )
            
            if self.video_writer and self.video_writer.isOpened():
                print("✅ نجح MJPG")
                return True
            else:
                if self.video_writer:
                    self.video_writer.release()
                print("❌ فشل MJPG")
        except Exception as e:
            print(f"❌ خطأ MJPG: {e}")
            
        # الطريقة الثانية: XVID
        try:
            print("🔧 تجربة XVID...")
            fourcc = cv2.VideoWriter_fourcc('X', 'V', 'I', 'D')
            self.video_writer = cv2.VideoWriter(
                self.video_filename, 
                fourcc, 
                float(self.fps), 
                (self.width, self.height)
            )
            
            if self.video_writer and self.video_writer.isOpened():
                print("✅ نجح XVID")
                return True
            else:
                if self.video_writer:
                    self.video_writer.release()
                print("❌ فشل XVID")
        except Exception as e:
            print(f"❌ خطأ XVID: {e}")
            
        # الطريقة الثالثة: بدون ضغط
        try:
            print("🔧 تجربة بدون ضغط...")
            self.video_writer = cv2.VideoWriter(
                self.video_filename, 
                0,  # بدون ضغط
                float(self.fps), 
                (self.width, self.height)
            )
            
            if self.video_writer and self.video_writer.isOpened():
                print("✅ نجح بدون ضغط")
                return True
            else:
                if self.video_writer:
                    self.video_writer.release()
                print("❌ فشل بدون ضغط")
        except Exception as e:
            print(f"❌ خطأ بدون ضغط: {e}")
            
        print("❌ فشل في جميع الطرق")
        return False

    def record_audio(self):
        """تسجيل الصوت"""
        try:
            print("🎵 بدء تسجيل الصوت...")

            # التحقق من تفعيل الصوت
            if not self.audio_enabled:
                print("⚠️ تسجيل الصوت معطل")
                return

            # فتح تدفق الصوت - جرب قناة واحدة أولاً
            try:
                self.audio_stream = self.audio.open(
                    format=self.audio_format,
                    channels=self.audio_channels,
                    rate=self.audio_rate,
                    input=True,
                    frames_per_buffer=self.audio_chunk
                )
            except Exception as e:
                print(f"⚠️ فشل في {self.audio_channels} قناة، جرب قناة واحدة: {e}")
                # جرب قناة واحدة (Mono)
                self.audio_channels = 1
                self.audio_stream = self.audio.open(
                    format=self.audio_format,
                    channels=self.audio_channels,
                    rate=self.audio_rate,
                    input=True,
                    frames_per_buffer=self.audio_chunk
                )

            print(f"🎤 بدء التقاط الصوت - {self.audio_channels} قناة، {self.audio_rate} Hz")
            frame_count = 0

            while self.recording:
                try:
                    data = self.audio_stream.read(self.audio_chunk, exception_on_overflow=False)
                    self.audio_frames.append(data)
                    frame_count += 1

                    # طباعة تقدم كل 100 إطار (حوالي كل ثانيتين)
                    if frame_count % 100 == 0:
                        print(f"🎵 تم تسجيل {frame_count} إطار صوتي")

                except Exception as e:
                    print(f"⚠️ خطأ في قراءة الصوت: {e}")
                    continue

            print(f"✅ انتهى تسجيل الصوت - تم تسجيل {frame_count} إطار")

        except Exception as e:
            print(f"❌ خطأ في تسجيل الصوت: {e}")
            self.audio_enabled = False
        finally:
            if self.audio_stream:
                self.audio_stream.stop_stream()
                self.audio_stream.close()

    def record_video(self):
        """تسجيل الفيديو"""
        try:
            with mss() as sct:
                # تحديد المنطقة للتسجيل
                if self.record_mode == "fullscreen":
                    monitor = sct.monitors[1]  # الشاشة الرئيسية
                    print(f"📺 بدء التقاط الشاشة كاملة: {monitor['width']}x{monitor['height']}")
                else:
                    # منطقة مخصصة
                    x, y, w, h = self.custom_region
                    monitor = {
                        "top": y,
                        "left": x,
                        "width": w,
                        "height": h
                    }
                    print(f"� بدء التقاط المنطقة المخصصة: {x},{y} - {w}x{h}")

                frame_count = 0
                start_time = time.time()

                while self.recording:
                    try:
                        # التقاط الشاشة
                        screenshot = sct.grab(monitor)
                        frame = np.array(screenshot)
                        frame = cv2.cvtColor(frame, cv2.COLOR_BGRA2BGR)

                        # إذا كانت المنطقة مخصصة، لا نحتاج لتغيير الحجم
                        if self.record_mode == "fullscreen":
                            # تغيير حجم الإطار للشاشة كاملة
                            frame = cv2.resize(frame, (self.width, self.height))
                        # للمنطقة المخصصة، نحتفظ بالحجم الأصلي

                        # إضافة مؤشر الماوس والنقرات
                        if self.show_mouse or self.show_clicks:
                            mouse_x, mouse_y = self.get_mouse_position()
                            clicked = self.is_mouse_clicked()
                            frame = self.draw_mouse_cursor(frame, mouse_x, mouse_y, clicked)

                        # كتابة الإطار
                        if self.video_writer:
                            self.video_writer.write(frame)

                        frame_count += 1

                        # تحديث الحالة كل ثانية
                        if frame_count % self.fps == 0:
                            elapsed = int(time.time() - start_time)
                            self.root.after(0, lambda: self.status_var.set(f"🔴 التسجيل: {elapsed} ثانية"))

                        # انتظار للحصول على معدل الإطارات المطلوب
                        time.sleep(1/self.fps)

                    except Exception as frame_error:
                        print(f"⚠️ خطأ في إطار: {frame_error}")
                        continue

                print(f"✅ انتهى التسجيل - تم تسجيل {frame_count} إطار")

        except Exception as e:
            print(f"❌ خطأ في تسجيل الفيديو: {e}")
            
    def stop_recording(self):
        """إيقاف التسجيل"""
        try:
            print("🛑 إيقاف التسجيل...")
            self.recording = False
            
            # انتظار انتهاء الخيوط
            if self.video_thread and self.video_thread.is_alive():
                self.video_thread.join(timeout=5)

            if self.audio_thread and self.audio_thread.is_alive():
                self.audio_thread.join(timeout=5)

            # إغلاق كاتب الفيديو
            if self.video_writer:
                self.video_writer.release()
                print("💾 تم إغلاق ملف الفيديو")

            # حفظ ملف الصوت ودمجه مع الفيديو
            if self.audio_enabled and self.audio_frames:
                self.save_audio_file()
                print("💾 تم حفظ ملف الصوت")
                # دمج الصوت مع الفيديو
                self.merge_audio_video()
                print("🎬 تم دمج الصوت مع الفيديو")
                
            # تحديث الواجهة
            self.start_btn.config(state='normal')
            self.stop_btn.config(state='disabled')
            self.status_var.set("✅ تم حفظ التسجيل")
            
            if self.audio_enabled and self.audio_frames:
                # التحقق من وجود ملف مدموج
                if os.path.exists(self.video_filename) and "final_recording" in self.video_filename:
                    success_msg = f"تم حفظ التسجيل بنجاح في:\n🎬 ملف مدموج (فيديو + صوت): {self.video_filename}"
                else:
                    success_msg = f"تم حفظ التسجيل بنجاح في:\n📹 فيديو: {self.video_filename}\n🎵 صوت: {self.audio_filename}"
            else:
                success_msg = f"تم حفظ التسجيل بنجاح في:\n📹 فيديو: {self.video_filename}\n⚠️ بدون صوت"
            messagebox.showinfo("نجح", success_msg)
            print("✅ تم إيقاف التسجيل وحفظ الملف")
            
        except Exception as e:
            error_msg = f"خطأ في إيقاف التسجيل:\n{str(e)}"
            messagebox.showerror("خطأ", error_msg)
            print(f"❌ خطأ في إيقاف التسجيل: {e}")

    def save_audio_file(self):
        """حفظ ملف الصوت"""
        try:
            with wave.open(self.audio_filename, 'wb') as wf:
                wf.setnchannels(self.audio_channels)
                wf.setsampwidth(self.audio.get_sample_size(self.audio_format))
                wf.setframerate(self.audio_rate)
                wf.writeframes(b''.join(self.audio_frames))
            print(f"✅ تم حفظ ملف الصوت: {self.audio_filename}")
        except Exception as e:
            print(f"❌ خطأ في حفظ الصوت: {e}")

    def merge_audio_video(self):
        """دمج الصوت مع الفيديو"""
        try:
            # التحقق من وجود الملفات
            if not os.path.exists(self.video_filename):
                print(f"❌ ملف الفيديو غير موجود: {self.video_filename}")
                return False

            if not os.path.exists(self.audio_filename):
                print(f"❌ ملف الصوت غير موجود: {self.audio_filename}")
                return False

            # إنشاء اسم الملف النهائي
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            final_filename = os.path.join(self.save_path, f"final_recording_{timestamp}.mp4")

            print("🎬 بدء دمج الصوت مع الفيديو...")
            print(f"📹 ملف الفيديو: {self.video_filename}")
            print(f"🎵 ملف الصوت: {self.audio_filename}")
            print(f"🎬 الملف النهائي: {final_filename}")

            # محاولة استخدام FFmpeg
            try:
                ffmpeg_cmd = [
                    'ffmpeg', '-y',  # -y للكتابة فوق الملف إذا كان موجوداً
                    '-i', self.video_filename,  # ملف الفيديو
                    '-i', self.audio_filename,  # ملف الصوت
                    '-c:v', 'copy',  # نسخ الفيديو بدون إعادة ترميز
                    '-c:a', 'aac',   # ترميز الصوت إلى AAC
                    '-strict', 'experimental',
                    final_filename
                ]

                print(f"🔧 تشغيل FFmpeg: {' '.join(ffmpeg_cmd)}")
                result = subprocess.run(ffmpeg_cmd, capture_output=True, text=True)

                if result.returncode == 0:
                    print(f"✅ تم دمج الملفات بنجاح: {final_filename}")
                    # حذف الملفات المؤقتة
                    try:
                        os.remove(self.video_filename)
                        os.remove(self.audio_filename)
                        print("🗑️ تم حذف الملفات المؤقتة")
                    except Exception as del_e:
                        print(f"⚠️ لا يمكن حذف الملفات المؤقتة: {del_e}")

                    # تحديث اسم الملف النهائي
                    self.video_filename = final_filename
                    return True
                else:
                    print(f"⚠️ فشل FFmpeg:")
                    print(f"stdout: {result.stdout}")
                    print(f"stderr: {result.stderr}")
                    raise Exception("FFmpeg failed")

            except FileNotFoundError:
                print("⚠️ FFmpeg غير متوفر، سيتم استخدام طريقة بديلة")
                raise Exception("FFmpeg not found")

        except Exception as e:
            print(f"⚠️ لا يمكن دمج الملفات: {e}")
            print("💡 ستحصل على ملفين منفصلين للفيديو والصوت")
            return False

def main():
    try:
        print("🚀 مسجل الشاشة مع تحديد المنطقة وتتبع الماوس")
        print("=" * 55)
        print("📋 المميزات:")
        print("   🖥️ تسجيل الشاشة كاملة")
        print("   📐 تحديد منطقة مخصصة للتسجيل")
        print("   🖱️ تتبع حركة الماوس والنقرات")
        print("   🎵 تسجيل الصوت مع الفيديو")
        print("   🎬 دمج تلقائي للصوت (مع FFmpeg)")
        print("   💾 تنسيق: MP4 (H.264) + WAV")
        print("=" * 55)
        print()

        print("🔧 إنشاء النافذة الرئيسية...")
        root = tk.Tk()
        print("🔧 إنشاء التطبيق...")
        app = FinalRecorder(root)
        print("✅ تم تحميل البرنامج بنجاح!")
    except Exception as e:
        print(f"❌ خطأ في تحميل البرنامج: {e}")
        import traceback
        traceback.print_exc()
        return
    
    def on_closing():
        if app.recording:
            if messagebox.askokcancel("إغلاق", "التسجيل جاري. هل تريد إيقافه والخروج؟"):
                app.stop_recording()
                time.sleep(1)
                # تنظيف موارد الصوت
                if hasattr(app, 'audio') and app.audio:
                    app.audio.terminate()
                root.destroy()
        else:
            # تنظيف موارد الصوت
            if hasattr(app, 'audio') and app.audio:
                app.audio.terminate()
            root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    
    try:
        root.mainloop()
    except Exception as e:
        print(f"❌ خطأ في البرنامج: {e}")

if __name__ == "__main__":
    main()
